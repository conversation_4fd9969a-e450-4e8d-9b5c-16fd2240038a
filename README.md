# 📊 Real-Time YouTube Data Analysis with <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and Spring Boot Web Applications

## 👥 Real Time Project Group 3

| Name | Matrix Number | Photo |
|------|---------------|-------|
| [Member 1 Name] | [Matrix Number] | ![Member 1](path/to/photo1.jpg) |
| [Member 2 Name] | [Matrix Number] | ![Member 2](path/to/photo2.jpg) |
| [Member 3 Name] | [Matrix Number] | ![Member 3](path/to/photo3.jpg) |
| [Member 4 Name] | [Matrix Number] | ![Member 4](path/to/photo4.jpg) |
| [Member 5 Name] | [Matrix Number] | ![Member 5](path/to/photo5.jpg) |

*Note: Please replace the placeholder information above with actual member details and photo paths.*

---

## 🎯 Project Overview

This project builds a **real-time distributed application** that fetches YouTube video data, processes top-liked comments, and sends selective results to a **Telegram bot** or a **consumer web dashboard**. It leverages:

- **Apache Kafka** for streaming data
- **Spring Boot** for producer/consumer apps
- **YouTube Data API** to retrieve video info and comments
- **Telegram <PERSON>** for comment notifications
- **MySQL** for link storage
- **Docker** for orchestration

---

## 🔄 System Flow

### ✅ 1. Spring Boot Producer Web App

- User submits **YouTube video links** through the web interface.
- Links are stored in a **MySQL database**.
- A **scheduled task** (every 30 seconds) performs the following:
  1. Fetch each video link from the database.
  2. Query the YouTube Data API for videos with **at least 1 comment**:
     - Channel Name
     - Subscribers
     - Total Videos
     - Total Comments (for the specific video)
     - Total Likes (for the specific video)
     - 🔥 **Up to 5 newest comments** (or all available if less than 5)
  3. Package the data and send it to **Apache Kafka**.

---

### 🧠 2. Kafka Processing Logic

Kafka receives the data and performs the following logic on each of the **newest comments** per video:

- If a **comment has odd character length**:
  - Send the following to **Telegram Bot**:
    ```
    📹 YouTube Link: <video link>
    📊 Channel Info:
    - Channel Name
    - Subscribers
    - Total Videos
    - Total Comments (for this video)
    - Total Likes (for this video)
    💬 Comment: <odd-length comment text>
    ```

- If a **comment has even character length**:
  - Send **video metadata** to the **consumer app**.
  - Send **each even comment** to the **consumer app**.
  - **All even comments are now processed** (no longer skipped after the first one).

---

### 📊 3. Consumer Web App

- Shows **individual video entries** for every video submitted (no channel deduplication).
- Each video displays:
  - **Channel Name**
  - **Subscribers** (channel-wide)
  - **Total Videos** (channel-wide)
  - **Total Comments** (for this specific video)
  - **Total Likes** (for this specific video)
  - **Even Comment** (the even-length comment for this video)
- Also provides analytics comparisons such as:
  - Channel with **most subscribers**
  - Channel with **most videos**
  - Channel with **most comments**
  - Channel with **most likes**
  - Channel with **highest average views**
  - Channel with **best engagement rate**

---

### 📲 4. Telegram Bot

- Displays **odd-length comments** in real-time.
- Each message includes:
  - YouTube link
  - Channel metadata
  - The comment text

✔️ Each message is complete and sent individually.

---

## 🔁 Real-Time Update Logic

- The producer periodically polls the YouTube API every 30 seconds.
- If a new comment is added to a video:
  - It will be **picked up during the next scheduled fetch**.
  - Kafka processes it and updates the respective output (Telegram/Consumer App).
- ⚠️ This real-time behavior is **polling-based**, not push-based.
- **Videos with at least 1 comment** are now processed (previously required 5+ comments).

---

## 🆕 Key System Improvements

### ✨ Recent Updates:
- **Lowered Comment Threshold**: Videos with 1+ comments (instead of 5+) are now processed
- **Individual Video Display**: Consumer app shows every video submitted, not just unique channels
- **Complete Even Comment Processing**: All even comments are processed for each video
- **Video-Specific Metrics**: Total Comments and Total Likes refer to individual videos
- **Enhanced Real-time Updates**: System processes and displays new video data every 30 seconds

---

## 🧩 Technical Stack

| Component       | Technology Used        |
|----------------|------------------------|
| UI Input       | Spring Boot + Thymeleaf |
| Backend Logic  | Spring Boot            |
| Messaging      | Apache Kafka (Centralized Config) |
| Bot Messaging  | Telegram Bot API       |
| Data Source    | YouTube Data API       |
| Database       | MySQL                  |
| Configuration  | Centralized kafka-config/ |
| Deployment     | Docker + Docker Compose|

---

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose installed
- YouTube Data API key
- Telegram Bot token

### Setup Steps
1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd rt-group-project
   ```

2. **Configure environment variables**
   ```bash
   cp env.example .env
   # Edit .env with your API keys
   ```

3. **Build and run the system**
   ```bash
   docker-compose up --build
   ```

4. **Access the applications**
   - Producer App: http://localhost:8080
   - Consumer App: http://localhost:8081
   - Telegram Bot: Search for your bot in Telegram

### Usage
1. Submit YouTube video links in the Producer App
2. View real-time analytics in the Consumer App
3. Receive odd-length comments via Telegram Bot

For detailed setup instructions, see [USER_GUIDE.md](USER_GUIDE.md)

---

## 📁 Project Structure

```
├── producer-app/          # Spring Boot producer application
├── consumer-app/          # Spring Boot consumer web dashboard
├── telegram-bot/          # Telegram bot service
├── shared/               # Shared data models
├── mysql/               # Database initialization scripts
├── kafka-config/         # Centralized Kafka configuration
│   ├── server.properties      # Kafka broker settings
│   ├── topics.properties      # Topic definitions
│   ├── producer.properties    # Producer configuration
│   ├── consumer.properties    # Consumer configuration
│   ├── application-kafka.yml  # Spring Boot integration
│   └── README.md              # Kafka config documentation
├── docker-compose.yml   # Docker orchestration
└── README.md           # This file
```

---


