#!/bin/bash

# ========================================================================================
# Kafka Migration Verification Script - YouTube Comment Bot Project
# ========================================================================================
# This script helps verify that the Kafka configuration migration was successful
# Run this after starting your system with: docker-compose up --build -d

echo "🚀 YouTube Comment Bot - Kafka Migration Verification"
echo "======================================================"
echo ""

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

echo -e "${BLUE}📁 Checking kafka-config folder structure...${NC}"
echo ""

# Check if kafka-config folder exists
if [ -d "kafka-config" ]; then
    echo -e "${GREEN}✅ kafka-config folder exists${NC}"
    
    # Check individual config files
    files=("server.properties" "topics.properties" "producer.properties" "consumer.properties" "application-kafka.yml" "README.md")
    
    for file in "${files[@]}"; do
        if [ -f "kafka-config/$file" ]; then
            echo -e "${GREEN}✅ kafka-config/$file exists${NC}"
        else
            echo -e "${RED}❌ kafka-config/$file missing${NC}"
        fi
    done
else
    echo -e "${RED}❌ kafka-config folder not found${NC}"
    exit 1
fi

echo ""
echo -e "${BLUE}🐳 Checking Docker containers...${NC}"
echo ""

# Check if docker-compose is available
if ! command_exists docker-compose; then
    echo -e "${RED}❌ docker-compose not found. Please install Docker Compose.${NC}"
    exit 1
fi

# Check if containers are running
containers=("youtube-kafka" "youtube-zookeeper" "youtube-producer" "youtube-consumer" "youtube-telegram-bot" "youtube-mysql")

for container in "${containers[@]}"; do
    if docker ps --format "table {{.Names}}" | grep -q "$container"; then
        echo -e "${GREEN}✅ $container is running${NC}"
    else
        echo -e "${YELLOW}⚠️  $container is not running${NC}"
    fi
done

echo ""
echo -e "${BLUE}📋 Checking Kafka topics...${NC}"
echo ""

# Check if Kafka container is running before testing topics
if docker ps --format "table {{.Names}}" | grep -q "youtube-kafka"; then
    echo "Waiting for Kafka to be ready..."
    sleep 5
    
    # List Kafka topics
    echo "Available topics:"
    if docker exec youtube-kafka kafka-topics.sh --list --bootstrap-server localhost:9092 2>/dev/null; then
        echo -e "${GREEN}✅ Kafka topics listed successfully${NC}"
    else
        echo -e "${RED}❌ Failed to list Kafka topics${NC}"
    fi
    
    echo ""
    echo "Checking specific topics:"
    topics=("youtube-odd-comments" "youtube-even-metadata" "youtube-even-comments" "youtube-control" "test-topic")
    
    for topic in "${topics[@]}"; do
        if docker exec youtube-kafka kafka-topics.sh --list --bootstrap-server localhost:9092 2>/dev/null | grep -q "$topic"; then
            echo -e "${GREEN}✅ Topic '$topic' exists${NC}"
        else
            echo -e "${YELLOW}⚠️  Topic '$topic' not found (will be auto-created)${NC}"
        fi
    done
else
    echo -e "${YELLOW}⚠️  Kafka container not running, skipping topic check${NC}"
fi

echo ""
echo -e "${BLUE}👥 Checking consumer groups...${NC}"
echo ""

if docker ps --format "table {{.Names}}" | grep -q "youtube-kafka"; then
    echo "Available consumer groups:"
    if docker exec youtube-kafka kafka-consumer-groups.sh --list --bootstrap-server localhost:9092 2>/dev/null; then
        echo -e "${GREEN}✅ Consumer groups listed successfully${NC}"
    else
        echo -e "${YELLOW}⚠️  No consumer groups found yet (normal on first startup)${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  Kafka container not running, skipping consumer group check${NC}"
fi

echo ""
echo -e "${BLUE}🌐 Checking application endpoints...${NC}"
echo ""

# Check application endpoints
endpoints=("http://localhost:8081" "http://localhost:8083")
names=("Producer App" "Consumer App")

for i in "${!endpoints[@]}"; do
    endpoint="${endpoints[$i]}"
    name="${names[$i]}"
    
    if command_exists curl; then
        if curl -s -o /dev/null -w "%{http_code}" "$endpoint" | grep -q "200\|302\|404"; then
            echo -e "${GREEN}✅ $name ($endpoint) is responding${NC}"
        else
            echo -e "${YELLOW}⚠️  $name ($endpoint) not responding${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  curl not available, cannot test $name endpoint${NC}"
    fi
done

echo ""
echo -e "${BLUE}📊 Configuration verification summary:${NC}"
echo ""

# Summary
echo "Migration Status:"
if [ -d "kafka-config" ] && [ -f "kafka-config/server.properties" ]; then
    echo -e "${GREEN}✅ Kafka configuration migration: SUCCESSFUL${NC}"
    echo -e "${GREEN}✅ All configuration files are in place${NC}"
    echo -e "${GREEN}✅ Project structure follows enterprise standards${NC}"
else
    echo -e "${RED}❌ Kafka configuration migration: INCOMPLETE${NC}"
fi

echo ""
echo -e "${BLUE}🎯 Next steps:${NC}"
echo ""
echo "1. If containers are not running, start them with:"
echo "   ${YELLOW}docker-compose up --build -d${NC}"
echo ""
echo "2. Test the system:"
echo "   • Producer App: ${YELLOW}http://localhost:8081${NC}"
echo "   • Consumer App: ${YELLOW}http://localhost:8083${NC}"
echo "   • Submit 5 YouTube URLs and verify functionality"
echo ""
echo "3. Check logs if needed:"
echo "   ${YELLOW}docker-compose logs -f${NC}"
echo ""
echo -e "${GREEN}🎉 Your project now has professional Kafka configuration!${NC}"
echo ""
